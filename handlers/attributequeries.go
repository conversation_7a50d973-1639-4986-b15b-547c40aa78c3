package handlers

import (
	"errors"
	"strconv"

	"github.com/Zomato/cdp-platform/dtos"
	utils "github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"

	"github.com/Zomato/cdp-platform/internal/authorization"
	"github.com/Zomato/cdp-platform/internal/manager"

	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetAllAttributeQueries(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to list all attribute queries",
		"method", r.Method,
		"path", r.URL.Path)

	queries, err := controllerManager.AttributeQueriesController.GetAllAttributeQueries(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, queries)
}

func GetAttributeQueryByID(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get attribute query by ID: ",
		"id", r.URL.Query().Get("id"),
		"method", r.Method,
		"path", r.URL.Path)

	queryIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if queryIDStr == "" {
		log.Info("No attribute query ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no attribute query ID associated with request"))
		return
	}

	queryID, err := strconv.ParseInt(queryIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid attribute query ID:", queryIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid attribute query ID"))
		return
	}

	log.Info("Received Get Attribute Query Request for ID:", queryID, r.URL)
	resp, err := controllerManager.AttributeQueriesController.GetAttributeQueryByID(ctx, queryID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, resp)
}

func ValidateAttributeQuery(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("Validate Attribute Query handler called")
	r := ctx.Request
	w := ctx.Writer

	var ValidateAttributeQueryRequest *dtos.ValidateAttributeQueryRequest
	err := ctx.ShouldBindJSON(&ValidateAttributeQueryRequest)
	if err != nil {
		log.Info("Error while decoding valdiate attribute query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(ValidateAttributeQueryRequest)

	resp, err := controllerManager.AttributeQueriesController.ValidateAttributeQuery(ctx, ValidateAttributeQueryRequest)
	if err != nil {
		log.Info("Error while preview query")
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, resp)
}

func ManualTrigger(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to manual trigger for attribute query", r)

	queryIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if queryIDStr == "" {
		log.Info("No attribute query ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no attribute query ID associated with request"))
		return
	}

	queryID, err := strconv.ParseInt(queryIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid attribute query ID:", queryIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid attribute query ID"))
		return
	}

	log.Info("Received to Manually trigger Attribute Query Request for ID:", queryID, r.URL)
	err = controllerManager.AttributeQueriesController.ManualTrigger(ctx, queryID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, "Manual Trigger Successful")
}

func UpdateQuery(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update query for attribute query")

	var attributeQueryUpdateQueryRequestDTO *dtos.AttributeQueryUpdateQueryRequest
	err := ctx.ShouldBindJSON(&attributeQueryUpdateQueryRequestDTO)
	if err != nil {
		log.Info("Error while decoding attribute query request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeQueryUpdateQueryRequestDTO)

	ownerID, err := controllerManager.AttributeQueriesController.GetAttributeQueryOwnerID(ctx, attributeQueryUpdateQueryRequestDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, ownerID); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	resp, err := controllerManager.AttributeQueriesController.UpdateQuery(ctx, attributeQueryUpdateQueryRequestDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func UpdateCron(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update cron for attribute query")

	var attributeQueryUpdateCronRequestDTO *dtos.AttributeQueryUpdateCronRequest
	err := ctx.ShouldBindJSON(&attributeQueryUpdateCronRequestDTO)
	if err != nil {
		log.Info("Error while decoding attribute query request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeQueryUpdateCronRequestDTO)

	ownerID, err := controllerManager.AttributeQueriesController.GetAttributeQueryOwnerID(ctx, attributeQueryUpdateCronRequestDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, ownerID); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	err = controllerManager.AttributeQueriesController.UpdateCron(ctx, attributeQueryUpdateCronRequestDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Cron Updated successfully!")
}

func UpdateEndDate(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update end date for attribute query")

	var attributeQueryUpdateEndDateRequestDTO *dtos.AttributeQueryUpdateEndDateRequest
	err := ctx.ShouldBindJSON(&attributeQueryUpdateEndDateRequestDTO)
	if err != nil {
		log.Info("Error while decoding attribute query end date request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeQueryUpdateEndDateRequestDTO)

	ownerID, err := controllerManager.AttributeQueriesController.GetAttributeQueryOwnerID(ctx, attributeQueryUpdateEndDateRequestDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, ownerID); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	err = controllerManager.AttributeQueriesController.UpdateEndDate(ctx, attributeQueryUpdateEndDateRequestDTO, false)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "End date updated successfully!")
}
